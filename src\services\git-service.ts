import tl from '../utils/taskWrapper';
import { SimpleGit, SimpleGitOptions, simpleGit } from 'simple-git';

export interface FileChange {
    fileName: string;
    changeType: 'added' | 'modified' | 'deleted';
    linesAdded: number;
    linesDeleted: number;
}

export interface PRAnalysis {
    totalFiles: number;
    totalLinesAdded: number;
    totalLinesDeleted: number;
    fileChanges: FileChange[];
    languages: string[];
}

export class GitService {
    private gitOptions: Partial<SimpleGitOptions> = {
        baseDir: `${tl.getVariable('System.DefaultWorkingDirectory')}`,
        binary: 'git',
    };

    private readonly _repository: SimpleGit;

    constructor() {
        this._repository = simpleGit(this.gitOptions);
        this._repository.addConfig('core.pager', 'cat');
        this._repository.addConfig('core.quotepath', 'false');

        // Configure authentication for Azure DevOps
        const accessToken = tl.getVariable('System.AccessToken');
        if (accessToken) {
            const collectionUri = tl.getVariable('System.TeamFoundationCollectionUri');
            if (collectionUri) {
                const authUrl = collectionUri.replace('https://', `https://PAT:${accessToken}@`);
                this._repository.addConfig('url.https://dev.azure.com/.insteadOf', 'https://dev.azure.com/');
                this._repository.addConfig('http.https://dev.azure.com/.extraheader', `Authorization: Basic ${Buffer.from(`PAT:${accessToken}`).toString('base64')}`);
            }
        }
    }

    public async init(): Promise<GitService> {
        try {
            await this._repository.fetch();
            console.info('Git repository initialized successfully');
            return this;
        } catch (error) {
            tl.warning(`Failed to initialize git repository: ${error}`);
            throw error;
        }
    }

    public async setupCurrentBranch(): Promise<void> {
        // Currently, this is only needed for dev mode. The Azure DevOps pipeline will automatically checkout the PR branch
        if (tl.isDev() && tl.getVariable('Auto_Setup_PR_Branch') === 'true') {
            const pullRequestBranch = `pull/${tl.getVariable('System_PullRequest_PullRequestId')}/merge`;
            console.info(`Updating PR branch: ${pullRequestBranch}`);

            try {
                // Update the local repository with the latest refspec from the remote repository
                await this._repository.fetch('origin', '+refs/pull/*/merge:refs/remotes/pull/*/merge');
                await this._repository.checkout(pullRequestBranch);

                const currentBranch = (await this._repository.branch()).current;
                console.info(`Current branch set to: ${currentBranch}`);
            } catch (error) {
                tl.warning(`Failed to setup PR branch: ${error}`);
                throw error;
            }
        }
    }

    public async getDiff(fileName: string): Promise<string> {
        try {
            const args = [this.getTargetBranch(), '--', fileName.replace(/^\//, '')];
            console.info('GetDiff()', args.join(' '));
            const diff = await this._repository.diff(args);
            return diff;
        } catch (error) {
            tl.warning(`Failed to get diff for file ${fileName}: ${error}`);
            throw error;
        }
    }

    public async getFullDiff(): Promise<string> {
        try {
            const targetBranch = this.getTargetBranch();
            console.info(`Getting full diff against ${targetBranch}`);
            const diff = await this._repository.diff([targetBranch]);
            return diff;
        } catch (error) {
            tl.warning(`Failed to get full diff: ${error}`);
            throw error;
        }
    }

    public async analyzePRChanges(files: string[]): Promise<PRAnalysis> {
        try {
            const fileChanges: FileChange[] = [];
            let totalLinesAdded = 0;
            let totalLinesDeleted = 0;
            const languages = new Set<string>();

            for (const file of files) {
                try {
                    const diff = await this.getDiff(file);
                    const analysis = this.analyzeDiff(diff, file);
                    
                    fileChanges.push(analysis);
                    totalLinesAdded += analysis.linesAdded;
                    totalLinesDeleted += analysis.linesDeleted;
                    
                    // Extract language from file extension
                    const language = this.getLanguageFromFile(file);
                    if (language) {
                        languages.add(language);
                    }
                } catch (error) {
                    tl.warning(`Failed to analyze file ${file}: ${error}`);
                    // Continue with other files
                }
            }

            return {
                totalFiles: fileChanges.length,
                totalLinesAdded,
                totalLinesDeleted,
                fileChanges,
                languages: Array.from(languages),
            };
        } catch (error) {
            tl.warning(`Failed to analyze PR changes: ${error}`);
            throw error;
        }
    }

    private analyzeDiff(diff: string, fileName: string): FileChange {
        const lines = diff.split('\n');
        let linesAdded = 0;
        let linesDeleted = 0;
        let changeType: 'added' | 'modified' | 'deleted' = 'modified';

        // Check if file is new or deleted
        if (diff.includes('new file mode')) {
            changeType = 'added';
        } else if (diff.includes('deleted file mode')) {
            changeType = 'deleted';
        }

        // Count added and deleted lines
        for (const line of lines) {
            if (line.startsWith('+') && !line.startsWith('+++')) {
                linesAdded++;
            } else if (line.startsWith('-') && !line.startsWith('---')) {
                linesDeleted++;
            }
        }

        return {
            fileName,
            changeType,
            linesAdded,
            linesDeleted,
        };
    }

    private getLanguageFromFile(fileName: string): string | null {
        const extension = fileName.split('.').pop()?.toLowerCase();
        
        const languageMap: { [key: string]: string } = {
            'ts': 'TypeScript',
            'js': 'JavaScript',
            'tsx': 'TypeScript React',
            'jsx': 'JavaScript React',
            'py': 'Python',
            'java': 'Java',
            'cs': 'C#',
            'cpp': 'C++',
            'c': 'C',
            'go': 'Go',
            'rs': 'Rust',
            'php': 'PHP',
            'rb': 'Ruby',
            'swift': 'Swift',
            'kt': 'Kotlin',
            'scala': 'Scala',
            'sql': 'SQL',
            'json': 'JSON',
            'yaml': 'YAML',
            'yml': 'YAML',
            'xml': 'XML',
            'html': 'HTML',
            'css': 'CSS',
            'scss': 'SCSS',
            'less': 'LESS',
            'md': 'Markdown',
            'sh': 'Shell',
            'ps1': 'PowerShell',
            'dockerfile': 'Docker',
        };

        return extension ? languageMap[extension] || null : null;
    }

    private getTargetBranch(): string {
        let target = tl.getVariable('System.PullRequest.TargetBranchName');

        if (!target) {
            target = tl.getVariable('System.PullRequest.TargetBranch')?.replace('refs/heads/', '');
        }

        if (!target) {
            throw new Error('Could not find target branch');
        }

        return `origin/${target}`;
    }

    public async getCommitMessages(maxCount: number = 10): Promise<string[]> {
        try {
            const targetBranch = this.getTargetBranch();
            const log = await this._repository.log({
                from: targetBranch,
                to: 'HEAD',
                maxCount,
            });

            return log.all.map(commit => commit.message);
        } catch (error) {
            tl.warning(`Failed to get commit messages: ${error}`);
            return [];
        }
    }

    public async getBranchInfo(): Promise<{ source: string; target: string }> {
        try {
            const sourceBranch = tl.getVariable('System.PullRequest.SourceBranch')?.replace('refs/heads/', '') || 'unknown';
            const targetBranch = tl.getVariable('System.PullRequest.TargetBranch')?.replace('refs/heads/', '') || 'unknown';

            return {
                source: sourceBranch,
                target: targetBranch,
            };
        } catch (error) {
            tl.warning(`Failed to get branch info: ${error}`);
            return { source: 'unknown', target: 'unknown' };
        }
    }
}
