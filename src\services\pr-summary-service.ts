import tl from '../utils/taskWrapper';
import { AIService, PRContext } from './ai-service';
import { PRService, PRSummary } from './pr-service';
import { GitService, PRAnalysis } from './git-service';
import { TaskConfig } from './config-service';

export class PRSummaryService {
    private aiService: AIService;
    private prService: PRService;
    private gitService: GitService;
    private config: TaskConfig;

    constructor(aiService: AIService, prService: PRService, gitService: GitService, config: TaskConfig) {
        this.aiService = aiService;
        this.prService = prService;
        this.gitService = gitService;
        this.config = config;
    }

    public async generateAndUpdatePRSummary(files: string[]): Promise<boolean> {
        try {
            if (!this.config.generateSummary) {
                console.info('PR summary generation is disabled');
                return true;
            }

            console.info('Starting PR summary generation...');

            // Get PR context
            const prContext = await this.buildPRContext();
            console.info(`PR Context: ${prContext.title} by ${prContext.author}`);

            // Analyze the changes
            const analysis = await this.gitService.analyzePRChanges(files);
            console.info(`Analysis complete: ${analysis.totalFiles} files, ${analysis.totalLinesAdded}+/${analysis.totalLinesDeleted}- lines`);

            // Get full diff for context (with size limits)
            const fullDiff = await this.getContextualDiff(files);

            // Generate summary using AI
            const summary = await this.aiService.generatePRSummary(analysis, fullDiff, prContext);
            console.info(`Generated summary: "${summary.title}"`);

            // Update PR description if enabled
            if (this.config.updatePrDescription) {
                const success = await this.prService.updatePRDescription(summary, this.config.summaryPlacement);
                if (success) {
                    console.info('Successfully updated PR description with AI summary');
                } else {
                    tl.warning('Failed to update PR description');
                }
                return success;
            } else {
                console.info('PR description update is disabled, summary generated but not applied');
                return true;
            }
        } catch (error) {
            tl.warning(`PR summary generation failed: ${error}`);
            return false;
        }
    }

    private async buildPRContext(): Promise<PRContext> {
        try {
            // Get PR details from Azure DevOps API
            const prDetails = await this.prService.getPRDetails();

            const branchInfo = await this.gitService.getBranchInfo();
            const commitMessages = await this.gitService.getCommitMessages(10);

            return {
                title: prDetails.title || tl.getVariable('System.PullRequest.PullRequestNumber') || 'Pull Request',
                description: prDetails.description || '',
                author: prDetails.createdBy?.displayName || tl.getVariable('Build.RequestedFor') || 'Unknown',
                sourceBranch: branchInfo.source,
                targetBranch: branchInfo.target,
                commitMessages,
            };
        } catch (error) {
            tl.warning(`Failed to build PR context: ${error}`);
            // Return fallback context using Azure DevOps variables
            return {
                title: tl.getVariable('System.PullRequest.PullRequestNumber') || 'Pull Request',
                description: '',
                author: tl.getVariable('Build.RequestedFor') || 'Unknown',
                sourceBranch: tl.getVariable('System.PullRequest.SourceBranch')?.replace('refs/heads/', '') || 'feature-branch',
                targetBranch: tl.getVariable('System.PullRequest.TargetBranch')?.replace('refs/heads/', '') || 'main',
                commitMessages: [],
            };
        }
    }

    private async getContextualDiff(files: string[]): Promise<string> {
        try {
            // For large PRs, limit the files we include in the diff
            const maxFilesForDiff = Math.min(files.length, this.config.maxFilesPerReview);
            const filesToInclude = files.slice(0, maxFilesForDiff);

            if (filesToInclude.length < files.length) {
                console.info(`Limiting diff context to ${maxFilesForDiff} files out of ${files.length} total`);
            }

            // Get individual file diffs and combine them
            const diffs: string[] = [];
            let totalSize = 0;
            const maxDiffSize = 50000; // Character limit for diff context

            for (const file of filesToInclude) {
                try {
                    const diff = await this.gitService.getDiff(file);
                    if (totalSize + diff.length > maxDiffSize) {
                        console.info(`Diff size limit reached, including ${diffs.length} files in context`);
                        break;
                    }
                    diffs.push(diff);
                    totalSize += diff.length;
                } catch (error) {
                    tl.warning(`Failed to get diff for ${file}: ${error}`);
                    // Continue with other files
                }
            }

            const combinedDiff = diffs.join('\n\n');
            console.info(`Generated contextual diff: ${combinedDiff.length} characters from ${diffs.length} files`);
            
            return combinedDiff;
        } catch (error) {
            tl.warning(`Failed to get contextual diff: ${error}`);
            return '';
        }
    }

    public async validateSummaryGeneration(): Promise<boolean> {
        try {
            // Check if AI service is available
            if (!this.aiService.isProviderAvailable()) {
                tl.warning(`AI provider ${this.aiService.getProviderName()} is not available`);
                return false;
            }

            // Check if we have necessary permissions
            const pr = await this.prService.getPullRequest();
            if (!pr) {
                tl.warning('Unable to access pull request information');
                return false;
            }

            console.info('PR summary generation validation passed');
            return true;
        } catch (error) {
            tl.warning(`PR summary validation failed: ${error}`);
            return false;
        }
    }

    public async generateSummaryPreview(files: string[]): Promise<PRSummary | null> {
        try {
            console.info('Generating PR summary preview...');

            const prContext = await this.buildPRContext();
            const analysis = await this.gitService.analyzePRChanges(files);
            const fullDiff = await this.getContextualDiff(files.slice(0, 5)); // Limit for preview

            const summary = await this.aiService.generatePRSummary(analysis, fullDiff, prContext);
            console.info('PR summary preview generated successfully');
            
            return summary;
        } catch (error) {
            tl.warning(`PR summary preview generation failed: ${error}`);
            return null;
        }
    }

    public formatSummaryForLogging(summary: PRSummary): string {
        return `
PR Summary Generated:
Title: ${summary.title}
Overview: ${summary.overview}
Key Changes: ${summary.keyChanges.length} items
Impact: ${summary.impact}
Recommendations: ${summary.recommendations?.length || 0} items
        `.trim();
    }
}
