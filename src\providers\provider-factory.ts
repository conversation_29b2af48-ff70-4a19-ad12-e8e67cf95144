import { TaskConfig } from '../services/config-service';
import { AIProvider } from '../services/ai-service';
import { OpenAIProvider, OpenAIConfig } from './openai-provider';
import { AnthropicProvider, AnthropicConfig } from './anthropic-provider';

export class ProviderFactory {
    public static createProvider(config: TaskConfig): AIProvider {
        switch (config.aiProvider) {
            case 'openai':
                return new OpenAIProvider(this.createOpenAIConfig(config));
            
            case 'azure-openai':
                return new OpenAIProvider(this.createAzureOpenAIConfig(config));
            
            case 'anthropic':
                return new AnthropicProvider(this.createAnthropicConfig(config));
            
            default:
                throw new Error(`Unsupported AI provider: ${config.aiProvider}`);
        }
    }

    private static createOpenAIConfig(config: TaskConfig): OpenAIConfig {
        return {
            apiKey: config.apiKey,
            model: config.azureModelDeployment || 'o3-mini',
            maxTokens: 128000,
            temperature: 0.1,
            additionalPrompts: config.additionalPrompts,
            bugs: config.bugs,
            performance: config.performance,
            bestPractices: config.bestPractices,
            modifiedLinesOnly: config.modifiedLinesOnly,
            enableCommentLineCorrection: config.enableCommentLineCorrection,
        };
    }

    private static createAzureOpenAIConfig(config: TaskConfig): OpenAIConfig {
        if (!config.azureApiEndpoint) {
            throw new Error('Azure API endpoint is required for Azure OpenAI provider');
        }

        return {
            apiKey: config.apiKey,
            model: config.azureModelDeployment || 'gpt-4o',
            azureApiEndpoint: config.azureApiEndpoint,
            azureApiVersion: config.azureApiVersion || '2024-10-21',
            azureModelDeployment: config.azureModelDeployment,
            maxTokens: 128000,
            temperature: 0.1,
            additionalPrompts: config.additionalPrompts,
            bugs: config.bugs,
            performance: config.performance,
            bestPractices: config.bestPractices,
            modifiedLinesOnly: config.modifiedLinesOnly,
            enableCommentLineCorrection: config.enableCommentLineCorrection,
        };
    }

    private static createAnthropicConfig(config: TaskConfig): AnthropicConfig {
        if (!config.anthropicApiKey) {
            throw new Error('Anthropic API key is required for Anthropic provider');
        }

        return {
            apiKey: config.anthropicApiKey,
            model: 'claude-3-5-sonnet-20241022',
            maxTokens: 200000,
            temperature: 0.1,
            additionalPrompts: config.additionalPrompts,
            bugs: config.bugs,
            performance: config.performance,
            bestPractices: config.bestPractices,
            modifiedLinesOnly: config.modifiedLinesOnly,
        };
    }

    public static getSupportedProviders(): string[] {
        return ['openai', 'azure-openai', 'anthropic'];
    }

    public static validateProviderConfig(config: TaskConfig): void {
        const supportedProviders = this.getSupportedProviders();
        
        if (!supportedProviders.includes(config.aiProvider)) {
            throw new Error(`Unsupported AI provider: ${config.aiProvider}. Supported providers: ${supportedProviders.join(', ')}`);
        }

        switch (config.aiProvider) {
            case 'openai':
                if (!config.apiKey) {
                    throw new Error('OpenAI API key is required');
                }
                break;
            
            case 'azure-openai':
                if (!config.apiKey) {
                    throw new Error('Azure OpenAI API key is required');
                }
                if (!config.azureApiEndpoint) {
                    throw new Error('Azure API endpoint is required');
                }
                break;
            
            case 'anthropic':
                if (!config.anthropicApiKey) {
                    throw new Error('Anthropic API key is required');
                }
                break;
        }
    }
}
