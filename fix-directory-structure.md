# Fix Directory Structure

The current structure has a nested `pr-inspection-assistant` directory which is confusing. Here's how to fix it:

## Current Structure (WRONG):
```
c:\Source\pr-inspection-assistant\
├── PR_SUMMARY_EXTENSION_PLAN.md
├── README.md
└── pr-inspection-assistant\          # <- This nested directory is the problem
    ├── src\
    ├── assets\
    └── vss-extension.json
```

## Target Structure (CORRECT):
```
c:\Source\pr-inspection-assistant\
├── PR_SUMMARY_EXTENSION_PLAN.md
├── README.md
├── src\                              # <- Move contents up one level
├── assets\
└── vss-extension.json
```

## Commands to Fix (run from `c:\Source\pr-inspection-assistant\`):

```bash
# Move all contents from nested directory up one level
mv pr-inspection-assistant/* .

# Remove the now-empty nested directory
rmdir pr-inspection-assistant

# Verify structure is correct
ls -la
```

## After fixing, you should be able to:

```bash
cd src
npm install
npm run build
```

This will resolve the directory confusion and make the project structure clean and logical.
