export interface PromptConfig {
    bugs?: boolean;
    performance?: boolean;
    bestPractices?: boolean;
    modifiedLinesOnly?: boolean;
    additionalPrompts?: string[];
}

export class PromptTemplates {
    public static buildCodeReviewPrompt(config: PromptConfig = {}): string {
        const { bugs, performance, bestPractices, modifiedLinesOnly, additionalPrompts } = config;
        
        let prompt = `Your task is to act as a code reviewer of a pull request within Azure DevOps.
        - You are provided with the code changes (diff) in a Unified Diff format.
        - You are provided with a file path (fileName).
        - You are provided with existing comments (existingComments) on the file, you must provide any additional code review comments that are not duplicates.
        - Do not highlight minor issues and nitpicks.
        ${modifiedLinesOnly ? '- Only comment on modified lines.' : ''}
        ${bugs ? '- If there are any bugs, highlight them.' : ''}
        ${performance ? '- If there are major performance problems, highlight them.' : ''}
        ${bestPractices ? '- Provide details on missed use of best-practices.' : '- Do not provide comments on best practices.'}`;

        if (additionalPrompts && additionalPrompts.length > 0) {
            prompt += '\n' + additionalPrompts.map(p => `- ${p}`).join('\n');
        }

        prompt += `

The response should be a single JSON object (without fenced codeblock) and it must use this sample JSON format:
{
    "threads": [
        // Use multiple, separate thread objects for distinct comments at different locations. Line and offset references should be as specific as possible.
        {
            "comments": [
                {
                    "content": "<Comment in markdown format without markdown fenced codeblock>",
                    "commentType": 2
                }
            ],
            "status": 1,
            "threadContext": {
                "filePath": "<string>", //path to file
                //only include leftFile properties for suggestions on unmodified lines
                "leftFileStart": {
                    "line": <integer>, //line where the suggestion starts
                    "offset": <integer>, //character offset where the suggestion starts
                    "snippet": "<code snippet for suggestion>"
                },
                "leftFileEnd": {
                    "line": <integer>, //line where the suggestion ends
                    "offset": <integer>, //character offset where the suggestion ends
                },
                //only use rightFile properties if the line changed in the diff
                "rightFileStart": {
                    "line": <integer>, //line where the suggestion starts
                    "snippet": "<code snippet for suggestion>",
                    "offset": <integer>, //character offset where the suggestion starts
                },
                "rightFileEnd": {
                    "line": <integer>, //line where the suggestion ends
                    "offset": <integer>, //character offset where the suggestion ends
                }
            }
        }
    ]
}`;

        return prompt;
    }

    public static buildPRSummaryPrompt(style: 'concise' | 'detailed' | 'executive' = 'detailed'): string {
        const styleInstructions = {
            concise: 'Keep the summary brief and to the point. Focus on the most important changes only. Limit to 2-3 sentences per section.',
            detailed: 'Provide a comprehensive summary with detailed analysis of changes and their implications. Include technical details that would be valuable to reviewers.',
            executive: 'Write for senior stakeholders. Focus on business impact, risks, and high-level technical decisions. Avoid implementation details.'
        };

        return `You are an expert code reviewer tasked with creating a pull request summary.

STYLE: ${styleInstructions[style]}

Analyze the provided pull request information and create a summary with the following structure:

1. **Title**: A clear, descriptive title for the changes (should be different from the PR title if it can be improved)
2. **Overview**: A paragraph explaining what this PR accomplishes and why it's needed
3. **Key Changes**: A bulleted list of the most important modifications (focus on what changed, not just file names)
4. **Impact**: Assessment of the change's impact on the codebase, users, and system behavior
5. **Recommendations**: Suggestions for reviewers, testing approaches, or areas needing special attention

Guidelines:
- Be accurate and objective based on the provided information
- Highlight potential risks or areas needing attention
- Consider the technical complexity and business value
- Use clear, professional language appropriate for the target audience
- Focus on what matters most to reviewers and stakeholders
- If the diff is large, focus on the most significant changes
- Identify patterns across multiple files when relevant

Input data includes:
- PR metadata (title, description, author, branches)
- Commit messages for context
- File change analysis (files modified, lines added/deleted, languages)
- Sample of the actual code diff

Respond with a JSON object in this exact format:
{
    "title": "Clear descriptive title that improves on the original if possible",
    "overview": "Comprehensive overview paragraph explaining what and why",
    "keyChanges": ["Specific change 1 with context", "Specific change 2 with context", "Specific change 3 with context"],
    "impact": "Impact assessment paragraph covering technical and business implications",
    "recommendations": ["Actionable recommendation 1", "Actionable recommendation 2", "Actionable recommendation 3"]
}`;
    }

    public static buildFileAnalysisPrompt(): string {
        return `Analyze the provided file diff and extract key information about the changes.

Focus on:
- Type of changes (new feature, bug fix, refactoring, etc.)
- Complexity level (simple, moderate, complex)
- Potential impact areas
- Notable patterns or concerns

Provide a brief, structured analysis that can be used to prioritize review efforts.`;
    }

    public static buildSecurityAnalysisPrompt(): string {
        return `Review the code changes for potential security vulnerabilities and concerns.

Look for:
- Input validation issues
- Authentication/authorization problems
- Data exposure risks
- Injection vulnerabilities
- Cryptographic issues
- Configuration security
- Dependency security

Highlight any security concerns found and provide specific recommendations.`;
    }

    public static buildPerformanceAnalysisPrompt(): string {
        return `Analyze the code changes for potential performance implications.

Consider:
- Algorithm efficiency
- Database query optimization
- Memory usage patterns
- Network call efficiency
- Caching opportunities
- Resource cleanup
- Scalability concerns

Identify performance improvements or potential bottlenecks.`;
    }
}
