import Anthropic from '@anthropic-ai/sdk';
import { Base<PERSON><PERSON>rovider, AIProviderConfig } from './base-provider';
import { Review } from '../types/review';
import { PRSummary } from '../services/pr-service';
import { PRAnalysis } from '../services/git-service';
import { PRContext } from '../services/ai-service';

export interface AnthropicConfig extends AIProviderConfig {
    model?: string;
}

export class AnthropicProvider extends BaseAIProvider {
    private client: Anthropic;
    private model: string;

    constructor(config: AnthropicConfig) {
        super(config);
        this.model = config.model || 'claude-3-5-sonnet-20241022';
        this.maxTokens = 200000; // <PERSON> has larger context window
        this.client = new Anthropic({
            apiKey: config.apiKey,
        });
    }

    public get name(): string {
        return 'Anthropic Claude';
    }

    public isAvailable(): boolean {
        return !!this.config.apiKey && !!this.client;
    }

    public async performCodeReview(diff: string, fileName: string, existingComments: string[]): Promise<Review> {
        try {
            const systemPrompt = this.buildCodeReviewPrompt();
            const userPrompt = this.formatUserPrompt({
                fileName: fileName.startsWith('/') ? fileName : `/${fileName}`,
                diff,
                existingComments,
            });

            const fullPrompt = systemPrompt + userPrompt;
            
            if (this.doesMessageExceedTokenLimit(fullPrompt, this.maxTokens)) {
                console.warn(`Diff for ${fileName} exceeds token limit, skipping review`);
                return { threads: [] };
            }

            const response = await this.client.messages.create({
                model: this.model,
                max_tokens: 4000,
                temperature: this.config.temperature || 0.1,
                system: systemPrompt,
                messages: [
                    {
                        role: 'user',
                        content: userPrompt,
                    },
                ],
            });

            const content = response.content[0];
            if (content.type !== 'text' || !content.text) {
                console.warn(`No text response content for ${fileName}`);
                return { threads: [] };
            }

            console.info(`Anthropic response for ${fileName}:\n${content.text}`);
            const review = JSON.parse(content.text) as Review;

            return review;
        } catch (error) {
            console.error(`Anthropic code review failed for ${fileName}: ${error}`);
            return { threads: [] };
        }
    }

    public async generatePRSummary(analysis: PRAnalysis, fullDiff: string, prContext: PRContext): Promise<PRSummary> {
        try {
            const systemPrompt = this.buildPRSummaryPrompt('detailed');
            
            // Prepare context data - Claude can handle more context
            const contextData = {
                prTitle: prContext.title,
                prDescription: prContext.description,
                author: prContext.author,
                sourceBranch: prContext.sourceBranch,
                targetBranch: prContext.targetBranch,
                commitMessages: prContext.commitMessages, // Include all commits for Claude
                analysis: {
                    totalFiles: analysis.totalFiles,
                    totalLinesAdded: analysis.totalLinesAdded,
                    totalLinesDeleted: analysis.totalLinesDeleted,
                    languages: analysis.languages,
                    fileChanges: analysis.fileChanges.map(fc => ({
                        fileName: fc.fileName,
                        changeType: fc.changeType,
                        linesAdded: fc.linesAdded,
                        linesDeleted: fc.linesDeleted,
                    })),
                },
                // Claude can handle larger diffs
                diffSample: this.truncateContent(fullDiff, 50000),
            };

            const userPrompt = this.formatUserPrompt(contextData);
            const fullPrompt = systemPrompt + userPrompt;

            if (this.doesMessageExceedTokenLimit(fullPrompt, this.maxTokens)) {
                console.warn('PR summary request exceeds token limit, using fallback');
                return this.createFallbackSummary(analysis, prContext);
            }

            const response = await this.client.messages.create({
                model: this.model,
                max_tokens: 2000,
                temperature: this.config.temperature || 0.3,
                system: systemPrompt,
                messages: [
                    {
                        role: 'user',
                        content: userPrompt,
                    },
                ],
            });

            const content = response.content[0];
            if (content.type !== 'text' || !content.text) {
                console.warn('No text response content for PR summary');
                return this.createFallbackSummary(analysis, prContext);
            }

            console.info(`Anthropic PR summary response:\n${content.text}`);
            const summary = JSON.parse(content.text) as PRSummary;
            
            return summary;
        } catch (error) {
            console.error(`Anthropic PR summary generation failed: ${error}`);
            return this.createFallbackSummary(analysis, prContext);
        }
    }

    private createFallbackSummary(analysis: PRAnalysis, prContext: PRContext): PRSummary {
        const keyChanges = analysis.fileChanges.slice(0, 8).map(change => {
            const action = change.changeType === 'added' ? 'Added' : 
                          change.changeType === 'deleted' ? 'Deleted' : 'Modified';
            return `${action} ${change.fileName} (+${change.linesAdded}/-${change.linesDeleted})`;
        });

        return {
            title: prContext.title || 'Pull Request Changes',
            overview: `This pull request modifies ${analysis.totalFiles} files with ${analysis.totalLinesAdded} additions and ${analysis.totalLinesDeleted} deletions. The changes span ${analysis.languages.join(', ')} codebases and appear to be ${this.inferChangeType(analysis)}.`,
            keyChanges,
            impact: this.assessImpact(analysis),
            recommendations: this.generateRecommendations(analysis)
        };
    }

    private inferChangeType(analysis: PRAnalysis): string {
        const addedFiles = analysis.fileChanges.filter(f => f.changeType === 'added').length;
        const deletedFiles = analysis.fileChanges.filter(f => f.changeType === 'deleted').length;
        const modifiedFiles = analysis.fileChanges.filter(f => f.changeType === 'modified').length;

        if (addedFiles > modifiedFiles && addedFiles > deletedFiles) {
            return 'primarily feature additions';
        } else if (deletedFiles > modifiedFiles) {
            return 'cleanup and refactoring';
        } else if (analysis.totalLinesAdded > analysis.totalLinesDeleted * 2) {
            return 'feature development';
        } else if (analysis.totalLinesDeleted > analysis.totalLinesAdded * 2) {
            return 'code reduction and optimization';
        } else {
            return 'maintenance and improvements';
        }
    }

    private assessImpact(analysis: PRAnalysis): string {
        const totalChanges = analysis.totalLinesAdded + analysis.totalLinesDeleted;
        const fileCount = analysis.totalFiles;

        if (totalChanges > 1000 || fileCount > 20) {
            return `High impact change affecting ${fileCount} files with ${totalChanges} total line changes. Requires thorough review and testing across ${analysis.languages.join(', ')} components.`;
        } else if (totalChanges > 200 || fileCount > 5) {
            return `Medium impact change affecting ${fileCount} files. Standard review process recommended for ${analysis.languages.join(', ')} modifications.`;
        } else {
            return `Low impact change with focused modifications to ${fileCount} files. Quick review should suffice.`;
        }
    }

    private generateRecommendations(analysis: PRAnalysis): string[] {
        const recommendations: string[] = [];
        
        if (analysis.totalFiles > 10) {
            recommendations.push('Consider breaking this PR into smaller, focused changes');
        }
        
        if (analysis.languages.length > 3) {
            recommendations.push('Multi-language changes detected - ensure cross-platform compatibility');
        }
        
        if (analysis.totalLinesAdded > 500) {
            recommendations.push('Large addition detected - verify comprehensive test coverage');
        }
        
        if (analysis.totalLinesDeleted > 200) {
            recommendations.push('Significant code removal - ensure no breaking changes');
        }

        // Always include basic recommendations
        recommendations.push('Verify all tests pass');
        recommendations.push('Check for any breaking changes');
        
        return recommendations;
    }
}
