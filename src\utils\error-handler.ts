import tl from './taskWrapper';

export class ErrorHandler {
    public static handleError(error: any, context: string): void {
        const errorMessage = this.formatError(error, context);
        console.error(errorMessage);
        tl.warning(errorMessage);
    }

    public static handleFatalError(error: any, context: string): never {
        const errorMessage = this.formatError(error, context);
        console.error(errorMessage);
        tl.setResult(tl.TaskResult.Failed, errorMessage);
        throw error;
    }

    public static async withRetry<T>(
        operation: () => Promise<T>,
        maxRetries: number = 3,
        delayMs: number = 1000,
        context: string = 'operation'
    ): Promise<T> {
        let lastError: any;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                
                if (attempt === maxRetries) {
                    this.handleError(error, `${context} failed after ${maxRetries} attempts`);
                    throw error;
                }

                const delay = delayMs * Math.pow(2, attempt - 1); // Exponential backoff
                console.warn(`${context} attempt ${attempt} failed, retrying in ${delay}ms: ${error}`);
                await this.delay(delay);
            }
        }

        throw lastError;
    }

    public static isRetryableError(error: any): boolean {
        if (!error) return false;

        // Network errors
        if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
            return true;
        }

        // HTTP errors that might be temporary
        if (error.status) {
            const status = error.status;
            return status === 429 || status === 502 || status === 503 || status === 504;
        }

        // OpenAI/Anthropic specific errors
        if (error.message) {
            const message = error.message.toLowerCase();
            return message.includes('rate limit') || 
                   message.includes('timeout') || 
                   message.includes('service unavailable');
        }

        return false;
    }

    private static formatError(error: any, context: string): string {
        if (error instanceof Error) {
            return `${context}: ${error.message}`;
        }
        
        if (typeof error === 'string') {
            return `${context}: ${error}`;
        }
        
        if (error && typeof error === 'object') {
            return `${context}: ${JSON.stringify(error)}`;
        }
        
        return `${context}: Unknown error occurred`;
    }

    private static delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

export function withErrorHandling<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    context: string
) {
    return async (...args: T): Promise<R | null> => {
        try {
            return await fn(...args);
        } catch (error) {
            ErrorHandler.handleError(error, context);
            return null;
        }
    };
}
