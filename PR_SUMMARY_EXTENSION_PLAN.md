# Azure DevOps AI PR Assistant - Rapid MVP Implementation Plan

## Project Overview

Transform the existing [PRIA foundation](https://github.com/ewellnitz/pr-inspection-assistant) into a production-ready Azure DevOps extension that automatically generates AI-powered pull request summaries and reviews. **Target: Working MVP with PR summaries by tomorrow.**

## Current State Assessment

✅ **Working Foundation**: Existing PRIA extension provides solid base with Azure DevOps integration
✅ **AI Integration**: OpenAI/Azure OpenAI already implemented and functional
✅ **Pipeline Integration**: Task execution and Azure DevOps API calls working
⚠️ **Architecture Gaps**: Monolithic design, no provider abstraction, limited extensibility
❌ **Missing Features**: PR summaries, multi-provider support, proper error handling

## Rapid MVP Strategy

### **Phase 1: Immediate MVP (Day 1 - Tomorrow)**
**Goal**: Working PR summaries with improved architecture foundation
- Refactor existing code into clean service layers
- Add PR summary generation capability
- Implement basic provider abstraction
- Ensure reliable PR description updates

### **Phase 2: Enhanced Features (Days 2-3)**
**Goal**: Multi-provider support and improved reliability
- Add Anthropic provider support
- Implement proper error handling and retries
- Add configuration validation
- Improve prompt templates

### **Phase 3: Production Ready (Days 4-5)**
**Goal**: Enterprise-ready extension with advanced features
- Add comprehensive testing
- Implement performance optimizations
- Add monitoring and logging
- Create deployment templates

## Architecture Vision

### 1. **Service-Oriented Architecture**
- **AI Service Layer**: Abstract provider interface with pluggable implementations
- **PR Service Layer**: Clean separation of Azure DevOps operations
- **Analysis Service**: Intelligent diff parsing and content processing
- **Configuration Service**: Centralized, validated configuration management

### 2. **Provider Pattern Implementation**
- **BaseAIProvider**: Common interface for all AI providers
- **OpenAIProvider**: Enhanced OpenAI integration with better error handling
- **AnthropicProvider**: Claude integration for longer context windows
- **ProviderFactory**: Smart provider selection and fallback logic

### 3. **Enhanced Capabilities**
- **PR Summaries**: High-level overview of changes with impact analysis
- **Smart Chunking**: Handle large PRs with intelligent content splitting
- **Template System**: Configurable prompts and output formats
- **Incremental Reviews**: Only review new changes, avoid duplicate work

## Target Architecture Structure

```
pr-inspection-assistant/
├── src/
│   ├── main.ts                         # 🔄 REFACTOR: Simplified orchestration
│   ├── services/
│   │   ├── ai-service.ts               # ✨ NEW: AI provider abstraction
│   │   ├── pr-summary-service.ts       # ✨ NEW: PR summary generation
│   │   ├── pr-service.ts               # 🔄 REFACTOR: From pullRequest.ts
│   │   ├── git-service.ts              # 🔄 REFACTOR: From repository.ts
│   │   └── config-service.ts           # ✨ NEW: Configuration management
│   ├── providers/
│   │   ├── base-provider.ts            # ✨ NEW: Provider interface
│   │   ├── openai-provider.ts          # 🔄 REFACTOR: From chatGpt.ts
│   │   ├── anthropic-provider.ts       # ✨ NEW: Claude integration
│   │   └── provider-factory.ts         # ✨ NEW: Provider selection
│   ├── utils/
│   │   ├── prompt-templates.ts         # ✨ NEW: Configurable prompts
│   │   ├── error-handler.ts            # ✨ NEW: Centralized error handling
│   │   └── file-utils.ts               # ✅ KEEP: Already good
│   ├── types/                          # ✅ KEEP: Well structured
│   └── legacy/                         # 📦 MOVE: Original files for reference
├── templates/
│   └── pr-summary-pipeline.yml         # ✨ NEW: Ready-to-use template
├── task.json                           # 🔄 UPDATE: Add PR summary options
├── vss-extension.json                  # 🔄 UPDATE: Version and features
└── package.json                        # 🔄 UPDATE: Add new dependencies
```

**Legend**: ✨ NEW | 🔄 REFACTOR | ✅ KEEP | 📦 MOVE

## Rapid Implementation Tasks (AI Agent Instructions)

### 🚀 **PHASE 1: MVP Implementation (Day 1 - PRIORITY)**

#### Task 1.1: Service Layer Foundation (2 hours)
**Agent Focus**: Refactor existing monolithic code into clean services

**Deliverables**:
- [ ] Extract `ConfigService` from scattered configuration logic
- [ ] Refactor `pullRequest.ts` → `PRService` with clean interface
- [ ] Refactor `repository.ts` → `GitService` with improved error handling
- [ ] Create `AIService` abstraction layer

**Files to refactor**:
- `src/services/config-service.ts` (extract from main.ts)
- `src/services/pr-service.ts` (refactor pullRequest.ts)
- `src/services/git-service.ts` (refactor repository.ts)
- `src/services/ai-service.ts` (new abstraction)

#### Task 1.2: AI Provider Abstraction (2 hours)
**Agent Focus**: Create provider pattern for AI services

**Deliverables**:
- [ ] Create `BaseAIProvider` interface
- [ ] Refactor existing `ChatGPT` class → `OpenAIProvider`
- [ ] Implement `ProviderFactory` with OpenAI support
- [ ] Extract prompt templates into configurable system

**Files to create**:
- `src/providers/base-provider.ts`
- `src/providers/openai-provider.ts` (refactor from chatGpt.ts)
- `src/providers/provider-factory.ts`
- `src/utils/prompt-templates.ts`

#### Task 1.3: PR Summary Generation (3 hours)
**Agent Focus**: Implement the core PR summary feature

**Deliverables**:
- [ ] Create `PRSummaryService` for generating high-level summaries
- [ ] Add PR description update functionality
- [ ] Implement summary prompt templates (concise, detailed, executive)
- [ ] Add configuration options for summary generation

**Files to create**:
- `src/services/pr-summary-service.ts`
- Update `task.json` with summary options
- Update prompt templates

#### Task 1.4: Main Orchestration Refactor (1 hour)
**Agent Focus**: Simplify main.ts to use new service architecture

**Deliverables**:
- [ ] Refactor `main.ts` to use dependency injection pattern
- [ ] Implement proper error handling with graceful degradation
- [ ] Add summary generation to the main workflow
- [ ] Ensure backward compatibility with existing functionality

**Files to update**:
- `src/main.ts` (major refactor)
- Add basic error handling utilities

### 🔧 **PHASE 2: Enhanced Features (Days 2-3)**

#### Task 2.1: Anthropic Provider (4 hours)
**Agent Focus**: Add Claude support for longer context windows

**Deliverables**:
- [ ] Implement `AnthropicProvider` using @anthropic-ai/sdk
- [ ] Add provider selection logic to factory
- [ ] Update configuration to support provider choice
- [ ] Test with large PR scenarios

#### Task 2.2: Advanced Error Handling (2 hours)
**Agent Focus**: Implement robust error handling and retries

**Deliverables**:
- [ ] Add exponential backoff for API calls
- [ ] Implement circuit breaker pattern
- [ ] Add comprehensive logging
- [ ] Handle rate limiting gracefully

#### Task 2.3: Smart Content Processing (3 hours)
**Agent Focus**: Improve handling of large PRs and content

**Deliverables**:
- [ ] Implement intelligent chunking for large diffs
- [ ] Add content sanitization for sensitive data
- [ ] Optimize token usage and cost management
- [ ] Add file type-specific processing

### 🏭 **PHASE 3: Production Ready (Days 4-5)**

#### Task 3.1: Testing Infrastructure (4 hours)
**Agent Focus**: Add comprehensive testing

**Deliverables**:
- [ ] Unit tests for all services
- [ ] Integration tests with mocked APIs
- [ ] End-to-end testing scenarios
- [ ] Performance testing for large PRs

#### Task 3.2: Deployment & Templates (2 hours)
**Agent Focus**: Create easy deployment options

**Deliverables**:
- [ ] Pipeline template for easy adoption
- [ ] Documentation and examples
- [ ] Version management and release process
- [ ] Migration guide from original PRIA

## Enhanced Task Configuration

### New Input Parameters (MVP)
```yaml
inputs:
  # Existing (keep all current inputs)
  - api_key: string (OpenAI/Azure OpenAI key)
  - ai_model: string (model selection)
  - bugs, performance, best_practices: boolean

  # NEW: AI Provider Selection
  - ai_provider: 'openai' | 'anthropic' | 'azure-openai' (default: 'openai')
  - anthropic_api_key: string (when using Anthropic)

  # NEW: PR Summary Features
  - generate_summary: boolean (default: true)
  - summary_style: 'concise' | 'detailed' | 'executive' (default: 'detailed')
  - update_pr_description: boolean (default: true)
  - summary_placement: 'top' | 'bottom' | 'replace' (default: 'top')

  # NEW: Enhanced Review Options
  - review_scope: 'all' | 'modified-only' | 'new-files' (default: 'modified-only')
  - max_files_per_review: number (default: 20)
  - enable_chunking: boolean (default: true)
```

### Enhanced Output Capabilities
- ✅ **PR Summary Generation**: High-level overview of changes and impact
- ✅ **PR Description Updates**: Automatically enhance PR descriptions
- ✅ **Detailed Code Review Comments**: Line-by-line feedback
- ✅ **Multi-Provider Support**: OpenAI, Anthropic, Azure OpenAI
- ✅ **Smart Content Processing**: Handle large PRs intelligently
- 🔄 **Advanced Analytics**: Cost tracking, performance metrics (Phase 2)
- 🔄 **Team Notifications**: Slack/Teams integration (Phase 3)

## Success Criteria & Validation

### 🎯 **MVP Success Criteria (Day 1)**
- [ ] **PR Summary Generation**: Creates meaningful high-level summaries of PR changes
- [ ] **PR Description Updates**: Successfully updates Azure DevOps PR descriptions
- [ ] **Multi-Provider Ready**: Clean architecture supports adding new AI providers
- [ ] **Backward Compatible**: Existing PRIA functionality continues to work
- [ ] **Deployable**: Extension packages and installs without issues

### 🧪 **Testing Strategy**
- **Unit Tests**: Core services and providers
- **Integration Tests**: Azure DevOps API interactions
- **Manual Testing**: Real PR scenarios with different sizes and types
- **Performance Testing**: Large PRs with 50+ files

### ⚡ **Quick Wins & Leverage Points**
- **Existing Foundation**: 80% of Azure DevOps integration already works
- **Proven Patterns**: Use established libraries (OpenAI SDK, Anthropic SDK)
- **Template Reuse**: Leverage existing prompt engineering from PRIA
- **Configuration Inheritance**: Build on existing task.json structure

## Implementation Guidelines for AI Agents

### 🤖 **Agent Instructions**

#### **Code Quality Standards**
- Use TypeScript with strict typing
- Implement proper error handling with try-catch blocks
- Add JSDoc comments for all public methods
- Follow existing code style and naming conventions
- Prefer composition over inheritance

#### **Dependencies to Add**
```json
{
  "@anthropic-ai/sdk": "^0.24.0",
  "axios": "^1.6.0",
  "lodash": "^4.17.21"
}
```

#### **Key Architectural Principles**
1. **Service Layer Pattern**: Each service has a single responsibility
2. **Provider Pattern**: All AI providers implement the same interface
3. **Configuration-Driven**: Behavior controlled through task inputs
4. **Error Resilience**: Graceful degradation when services fail
5. **Backward Compatibility**: Don't break existing PRIA functionality

#### **Testing Requirements**
- Mock all external API calls (Azure DevOps, OpenAI, Anthropic)
- Test error scenarios (API failures, rate limits, invalid responses)
- Validate configuration parsing and validation
- Test large PR scenarios with chunking

### 🚀 **Deployment Strategy**
1. **Development**: Test with local .env.local setup
2. **Staging**: Deploy to test Azure DevOps organization
3. **Production**: Package as .vsix and publish to marketplace
4. **Rollback Plan**: Keep original PRIA version available

This rapid implementation plan focuses on delivering working PR summaries within 24 hours while establishing a solid architectural foundation for future enhancements.