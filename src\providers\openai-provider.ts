import { OpenAI, AzureOpenAI } from 'openai';
import { encode } from 'gpt-tokenizer';
import { BaseAIProvider, AIProviderConfig } from './base-provider';
import { Review } from '../types/review';
import { PRSummary } from '../services/pr-service';
import { PRAnalysis } from '../services/git-service';
import { PRContext } from '../services/ai-service';
import { CommentLineNumberAndOffsetFixer } from '../utils/commentLineNumberAndOffsetFixer';

export interface OpenAIConfig extends AIProviderConfig {
    azureApiEndpoint?: string;
    azureApiVersion?: string;
    azureModelDeployment?: string;
    enableCommentLineCorrection?: boolean;
}

type Client = OpenAI | AzureOpenAI;

export class OpenAIProvider extends BaseAIProvider {
    private client: Client;
    private model: string;
    private enableCommentLineCorrection: boolean;

    constructor(config: OpenAIConfig) {
        super(config);
        this.model = config.model || 'o3-mini';
        this.enableCommentLineCorrection = config.enableCommentLineCorrection || false;
        this.client = this.createClient(config);
    }

    public get name(): string {
        return 'OpenAI';
    }

    public isAvailable(): boolean {
        return !!this.config.apiKey && !!this.client;
    }

    private createClient(config: OpenAIConfig): Client {
        if (config.azureApiEndpoint) {
            return new AzureOpenAI({
                apiKey: config.apiKey,
                endpoint: config.azureApiEndpoint,
                apiVersion: config.azureApiVersion || '2024-10-21',
                deployment: config.azureModelDeployment || this.model,
            });
        } else {
            return new OpenAI({ apiKey: config.apiKey });
        }
    }

    public async performCodeReview(diff: string, fileName: string, existingComments: string[]): Promise<Review> {
        try {
            const systemPrompt = this.buildCodeReviewPrompt();
            const userPrompt = this.formatUserPrompt({
                fileName: fileName.startsWith('/') ? fileName : `/${fileName}`,
                diff,
                existingComments,
            });

            const fullPrompt = systemPrompt + userPrompt;
            
            if (this.doesMessageExceedTokenLimit(fullPrompt, this.maxTokens)) {
                console.warn(`Diff for ${fileName} exceeds token limit, skipping review`);
                return { threads: [] };
            }

            const response = await this.client.chat.completions.create({
                messages: [
                    {
                        role: this.isO1Model(this.model) ? 'assistant' : 'system',
                        content: systemPrompt,
                    },
                    {
                        role: 'user',
                        content: userPrompt,
                    },
                ],
                model: this.model,
                temperature: this.config.temperature || 0.1,
            });

            const content = response.choices[0]?.message?.content;
            if (!content) {
                console.warn(`No response content for ${fileName}`);
                return { threads: [] };
            }

            console.info(`OpenAI response for ${fileName}:\n${content}`);
            const review = JSON.parse(content) as Review;

            // Apply line number correction if enabled
            if (this.enableCommentLineCorrection) {
                CommentLineNumberAndOffsetFixer.fix(review, diff);
            }

            return review;
        } catch (error) {
            console.error(`OpenAI code review failed for ${fileName}: ${error}`);
            return { threads: [] };
        }
    }

    public async generatePRSummary(analysis: PRAnalysis, fullDiff: string, prContext: PRContext): Promise<PRSummary> {
        try {
            const systemPrompt = this.buildPRSummaryPrompt('detailed');
            
            // Prepare context data
            const contextData = {
                prTitle: prContext.title,
                prDescription: prContext.description,
                author: prContext.author,
                sourceBranch: prContext.sourceBranch,
                targetBranch: prContext.targetBranch,
                commitMessages: prContext.commitMessages.slice(0, 5), // Limit to recent commits
                analysis: {
                    totalFiles: analysis.totalFiles,
                    totalLinesAdded: analysis.totalLinesAdded,
                    totalLinesDeleted: analysis.totalLinesDeleted,
                    languages: analysis.languages,
                    keyFiles: analysis.fileChanges.slice(0, 10).map(fc => ({
                        fileName: fc.fileName,
                        changeType: fc.changeType,
                        linesAdded: fc.linesAdded,
                        linesDeleted: fc.linesDeleted,
                    })),
                },
                // Include a truncated version of the diff for context
                diffSample: this.truncateContent(fullDiff, 8000), // Reserve tokens for response
            };

            const userPrompt = this.formatUserPrompt(contextData);
            const fullPrompt = systemPrompt + userPrompt;

            if (this.doesMessageExceedTokenLimit(fullPrompt, this.maxTokens)) {
                console.warn('PR summary request exceeds token limit, using fallback');
                return this.createFallbackSummary(analysis, prContext);
            }

            const response = await this.client.chat.completions.create({
                messages: [
                    {
                        role: this.isO1Model(this.model) ? 'assistant' : 'system',
                        content: systemPrompt,
                    },
                    {
                        role: 'user',
                        content: userPrompt,
                    },
                ],
                model: this.model,
                temperature: this.config.temperature || 0.3,
            });

            const content = response.choices[0]?.message?.content;
            if (!content) {
                console.warn('No response content for PR summary');
                return this.createFallbackSummary(analysis, prContext);
            }

            console.info(`OpenAI PR summary response:\n${content}`);
            const summary = JSON.parse(content) as PRSummary;
            
            return summary;
        } catch (error) {
            console.error(`OpenAI PR summary generation failed: ${error}`);
            return this.createFallbackSummary(analysis, prContext);
        }
    }

    private createFallbackSummary(analysis: PRAnalysis, prContext: PRContext): PRSummary {
        const keyChanges = analysis.fileChanges.slice(0, 5).map(change => {
            const action = change.changeType === 'added' ? 'Added' : 
                          change.changeType === 'deleted' ? 'Deleted' : 'Modified';
            return `${action} ${change.fileName} (+${change.linesAdded}/-${change.linesDeleted})`;
        });

        return {
            title: prContext.title || 'Pull Request Changes',
            overview: `This pull request contains ${analysis.totalFiles} file changes with ${analysis.totalLinesAdded} additions and ${analysis.totalLinesDeleted} deletions across ${analysis.languages.join(', ')} files.`,
            keyChanges,
            impact: `Medium impact change affecting ${analysis.totalFiles} files. Review recommended for ${analysis.languages.join(', ')} components.`,
            recommendations: [
                'Review the changes carefully',
                'Test the functionality thoroughly',
                'Ensure all tests pass'
            ]
        };
    }

    private isO1Model(model: string): boolean {
        return model === 'o1-preview' || model === 'o1-mini';
    }

    protected doesMessageExceedTokenLimit(message: string, tokenLimit: number): boolean {
        try {
            const tokens = encode(message);
            console.info(`Token count: ${tokens.length}`);
            return tokens.length > tokenLimit;
        } catch (error) {
            console.warn(`Failed to count tokens, using character approximation: ${error}`);
            return super.doesMessageExceedTokenLimit(message, tokenLimit);
        }
    }
}
