{"manifestVersion": 1, "id": "ec-cody", "version": "2.2.8", "name": "EC Cody", "publisher": "EC-Alex", "public": false, "targets": [{"id": "Microsoft.VisualStudio.Services"}], "description": "Automate pull request reviews in Azure DevOps using OpenAI or Azure OpenAI APIs. This bot analyzes code changes, offers suggestions, detects potential bugs, and ensures adherence to coding standards.", "categories": ["Azure Pipelines"], "tags": ["PR", "Code Analysis", "Open AI", "OpenAI", "Code Quality", "ChatGPT", "Quality Assurance", "Pull Request", "Pipeline", "Code Inspection", "Peer Review", "Code Review", "AI"], "icons": {"default": "src/assets/logo.png"}, "branding": {"color": "rgb(138, 43, 226)", "theme": "dark"}, "links": {"support": {"uri": "https://github.com/ewellnitz/pr-inspection-assistant", "version": "1.0.0"}}, "repository": {"type": "git", "uri": "https://github.com/ewellnitz/pr-inspection-assistant"}, "screenshots": [], "content": {"details": {"path": "src/assets/overview.md"}}, "files": [{"path": "dist"}, {"path": "src/assets/overview.md", "addressable": true}, {"path": "src/assets/logo.png", "addressable": true}, {"path": "dist/task.json", "addressable": true}], "contributions": [{"id": "custom-build-release-task", "type": "ms.vss-distributed-task.task", "targets": ["ms.vss-distributed-task.tasks"], "properties": {"name": "dist"}}]}