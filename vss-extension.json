{"manifestVersion": 1, "id": "ec-cody", "version": "2.2.1", "name": "EC Cody", "publisher": "Engineered Code", "public": true, "targets": [{"id": "Microsoft.VisualStudio.Services"}], "description": "Automate pull request reviews in Azure DevOps using OpenAI or Azure OpenAI APIs. This bot analyzes code changes, offers suggestions, detects potential bugs, and ensures adherence to coding standards.", "categories": ["Azure Pipelines"], "tags": ["PR", "Code Analysis", "Open AI", "OpenAI", "Code Quality", "ChatGPT", "Quality Assurance", "Pull Request", "Pipeline", "Code Inspection", "Peer Review", "Code Review", "AI"], "icons": {"default": "assets/logo.png"}, "branding": {"color": "rgb(138, 43, 226)", "theme": "dark"}, "links": {"support": {"uri": "https://github.com/ewellnitz/pr-inspection-assistant", "version": "1.0.0"}}, "repository": {"type": "git", "uri": "https://github.com/ewellnitz/pr-inspection-assistant"}, "screenshots": [], "content": {"details": {"path": "assets/overview.md"}}, "files": [{"path": "src"}, {"path": "assets/overview.md", "addressable": true}, {"path": "assets/logo.png", "addressable": true}], "contributions": [{"id": "custom-build-release-task", "type": "ms.vss-distributed-task.task", "targets": ["ms.vss-distributed-task.tasks"], "properties": {"name": "src"}}]}