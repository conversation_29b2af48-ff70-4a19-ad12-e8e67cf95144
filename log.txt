2025-07-18T11:55:11.0039522Z ##[section]Starting: PRIA
2025-07-18T11:55:11.0057330Z ==============================================================================
2025-07-18T11:55:11.0057906Z Task         : PR Inspection Assistant
2025-07-18T11:55:11.0058195Z Description  : Complete a Code Review using OpenAI
2025-07-18T11:55:11.0058519Z Version      : 2.2.9
2025-07-18T11:55:11.0058841Z Author       : <PERSON>
2025-07-18T11:55:11.0059097Z Help         : 
2025-07-18T11:55:11.0059309Z ==============================================================================
2025-07-18T11:55:11.5054751Z isDev:  false
2025-07-18T11:55:11.5055391Z verboseLogging:  true
2025-07-18T11:55:11.5416168Z getInput: api_key = ********************************************************************************************************************************************************************
2025-07-18T11:55:11.5417255Z getInput: api_endpoint = undefined
2025-07-18T11:55:11.5417871Z getInput: api_version = undefined
2025-07-18T11:55:11.5418487Z getInput: ai_model = o3-mini
2025-07-18T11:55:11.5419199Z getInput: file_extensions = undefined
2025-07-18T11:55:11.5419818Z getInput: file_extension_excludes = undefined
2025-07-18T11:55:11.5420453Z getInput: file_includes = undefined
2025-07-18T11:55:11.5421056Z getInput: file_excludes = undefined
2025-07-18T11:55:11.5421670Z getInput: additional_prompts = undefined
2025-07-18T11:55:11.5422269Z getBoolInput: bugs = false
2025-07-18T11:55:11.5422846Z getBoolInput: performance = false
2025-07-18T11:55:11.5423633Z getBoolInput: best_practices = false
2025-07-18T11:55:11.5424257Z getBoolInput: modified_lines_only = true
2025-07-18T11:55:11.5425321Z getBoolInput: comment_line_correction = true
2025-07-18T11:55:11.5425942Z getBoolInput: allow_requeue = true
2025-07-18T11:55:11.5426533Z getInput: ai_provider = openai
2025-07-18T11:55:11.5427108Z getInput: anthropic_api_key = undefined
2025-07-18T11:55:11.5427722Z getBoolInput: generate_summary = true
2025-07-18T11:55:11.5428328Z getInput: summary_style = detailed
2025-07-18T11:55:11.5428961Z getBoolInput: update_pr_description = true
2025-07-18T11:55:11.5429574Z getInput: summary_placement = top
2025-07-18T11:55:11.5430167Z getInput: review_scope = undefined
2025-07-18T11:55:11.5431582Z getInput: max_files_per_review = undefined
2025-07-18T11:55:11.5432947Z getBoolInput: enable_chunking = false
2025-07-18T11:55:11.5434323Z Configuration validated successfully
2025-07-18T11:55:11.5435568Z getVariable: System.TeamFoundationCollectionUri = https://dev.azure.com/alex0855/
2025-07-18T11:55:11.5436901Z getVariable: System.TeamProjectId = 07f724d3-d18c-470f-9691-c4c7345de493
2025-07-18T11:55:11.5438097Z getVariable: Build.Repository.Name = EC-Cody
2025-07-18T11:55:11.5439296Z getVariable: System.PullRequest.PullRequestId = 2
2025-07-18T11:55:11.5440372Z getVariable: System.DefaultWorkingDirectory = /home/<USER>/work/1/s
2025-07-18T11:55:11.5513461Z getVariable: System.AccessToken = ***
2025-07-18T11:55:11.5514806Z getVariable: System.TeamFoundationCollectionUri = https://dev.azure.com/alex0855/
2025-07-18T11:55:11.8309952Z All services initialized successfully
2025-07-18T11:55:11.8314993Z getVariable: Build.Reason = PullRequest
2025-07-18T11:55:11.8321644Z getVariable: System.AccessToken = ***
2025-07-18T11:55:11.8322308Z Task Configuration:
2025-07-18T11:55:11.8322840Z   apiKey: ***
2025-07-18T11:55:11.8323645Z   azureApiEndpoint: undefined
2025-07-18T11:55:11.8324247Z   azureApiVersion: undefined
2025-07-18T11:55:11.8324853Z   azureModelDeployment: o3-mini
2025-07-18T11:55:11.8325436Z   fileExtensions: undefined
2025-07-18T11:55:11.8326012Z   fileExtensionExcludes: undefined
2025-07-18T11:55:11.8326630Z   filesToInclude: undefined
2025-07-18T11:55:11.8327250Z   filesToExclude: undefined
2025-07-18T11:55:11.8327791Z   additionalPrompts: 
2025-07-18T11:55:11.8328302Z   bugs: false
2025-07-18T11:55:11.8328838Z   performance: false
2025-07-18T11:55:11.8329425Z   bestPractices: false
2025-07-18T11:55:11.8329972Z   modifiedLinesOnly: true
2025-07-18T11:55:11.8330532Z   enableCommentLineCorrection: true
2025-07-18T11:55:11.8331632Z   allowRequeue: true
2025-07-18T11:55:11.8332151Z   aiProvider: openai
2025-07-18T11:55:11.8332678Z   anthropicApiKey: undefined
2025-07-18T11:55:11.8337836Z   generateSummary: true
2025-07-18T11:55:11.8338534Z   summaryStyle: detailed
2025-07-18T11:55:11.8339066Z   updatePrDescription: true
2025-07-18T11:55:11.8339588Z   summaryPlacement: top
2025-07-18T11:55:11.8340210Z   reviewScope: modified-only
2025-07-18T11:55:11.8340734Z   maxFilesPerReview: 20
2025-07-18T11:55:11.8341251Z   enableChunking: false
2025-07-18T11:55:12.0336490Z Git repository initialized successfully
2025-07-18T11:55:12.0366854Z getVariable: System.AccessToken = ***
2025-07-18T11:55:12.2087739Z No last reviewed iteration found, returning default range.
2025-07-18T11:55:12.2105317Z getVariable: System.AccessToken = ***
2025-07-18T11:55:12.3124995Z Latest iteration ID: 5
2025-07-18T11:55:12.3127552Z Review range: 0 to 5, isRequeued: false
2025-07-18T11:55:12.3132183Z getVariable: System.AccessToken = ***
2025-07-18T11:55:12.4072776Z Found 1 changed files in this run: [ '/azure-pipelines.yml' ]
2025-07-18T11:55:12.4077141Z After filtering, 1 files will be reviewed: [ '/azure-pipelines.yml' ]
2025-07-18T11:55:12.4079400Z Generating PR summary...
2025-07-18T11:55:12.4080014Z Starting PR summary generation...
2025-07-18T11:55:12.4089467Z getVariable: System.AccessToken = ***
2025-07-18T11:55:12.4987326Z getVariable: System.PullRequest.SourceBranch = refs/heads/azure-pipelines
2025-07-18T11:55:12.4988299Z getVariable: System.PullRequest.TargetBranch = refs/heads/main
2025-07-18T11:55:12.4989083Z getVariable: System.PullRequest.TargetBranchName = main
2025-07-18T11:55:12.5075734Z PR Context: Set up CI with Azure Pipelines by Alex Bisztray
2025-07-18T11:55:12.5079529Z getVariable: System.PullRequest.TargetBranchName = main
2025-07-18T11:55:12.5084305Z GetDiff() origin/main -- azure-pipelines.yml
2025-07-18T11:55:12.5160641Z Analysis complete: 1 files, 18+/0- lines
2025-07-18T11:55:12.5167028Z getVariable: System.PullRequest.TargetBranchName = main
2025-07-18T11:55:12.5168037Z GetDiff() origin/main -- azure-pipelines.yml
2025-07-18T11:55:12.5242771Z Generated contextual diff: 696 characters from 1 files
2025-07-18T11:55:12.5243891Z Generating PR summary using OpenAI
2025-07-18T11:55:12.5318534Z Token count: 711
2025-07-18T11:55:12.9689161Z OpenAI PR summary generation failed: Error: 404 The model `o3-mini` does not exist or you do not have access to it.
2025-07-18T11:55:12.9691227Z PR summary generated successfully
2025-07-18T11:55:12.9691962Z Generated summary: "Set up CI with Azure Pipelines"
2025-07-18T11:55:12.9696419Z getVariable: System.AccessToken = ***
2025-07-18T11:55:13.0535527Z getVariable: System.AccessToken = ***
2025-07-18T11:55:13.0541848Z getVariable: System.AccessToken = ***
2025-07-18T11:55:13.1318114Z ##[warning]ADO Failed to fetch: PATCH https://dev.azure.com/alex0855/07f724d3-d18c-470f-9691-c4c7345de493/_apis/git/repositories/EC-Cody/pullRequests/2?api-version=7.0. Response: Unsupported Media Type
2025-07-18T11:55:13.1331501Z ##[warning]Failed to update PR description: Unsupported Media Type
2025-07-18T11:55:13.1336651Z ##[warning]Failed to update PR description
2025-07-18T11:55:13.1339502Z ##[warning]PR summary generation failed, continuing with code review
2025-07-18T11:55:13.1342643Z Reviewing file 1/1: /azure-pipelines.yml
2025-07-18T11:55:13.1343579Z getVariable: System.PullRequest.TargetBranchName = main
2025-07-18T11:55:13.1344329Z GetDiff() origin/main -- azure-pipelines.yml
2025-07-18T11:55:13.1416728Z getVariable: SYSTEM.TEAMPROJECT = EC-Cody
2025-07-18T11:55:13.1435712Z getVariable: System.AccessToken = ***
2025-07-18T11:55:13.2497276Z getVariable: System.AccessToken = ***
2025-07-18T11:55:13.3512579Z Found 1 existing comments for /azure-pipelines.yml
2025-07-18T11:55:13.3537841Z Performing code review for /azure-pipelines.yml using OpenAI
2025-07-18T11:55:13.3543450Z Token count: 768
2025-07-18T11:55:13.8458358Z OpenAI code review failed for /azure-pipelines.yml: Error: 404 The model `o3-mini` does not exist or you do not have access to it.
2025-07-18T11:55:13.8460363Z Code review completed for /azure-pipelines.yml. Found 0 issues
2025-07-18T11:55:13.8461092Z No review comments generated for /azure-pipelines.yml
2025-07-18T11:55:13.8464756Z Code review completed for 1 files
2025-07-18T11:55:13.8469786Z getVariable: System.AccessToken = ***
2025-07-18T11:55:13.8475846Z getVariable: System.AccessToken = ***
2025-07-18T11:55:13.9645204Z Saved last reviewed iteration 0-5
2025-07-18T11:55:13.9762748Z ##[section]Finishing: PRIA