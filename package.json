{"name": "pr-gpt", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "postbuild": "node -e \"require('fs').copyFileSync('src/task.json', 'dist/task.json')\"", "build:watch": "tsc -w", "package": "npm run build && npm run test && tfx extension create --manifest-globs vss-extension.json", "dev": "npm run build && dotenvx run -f .env.local -- npx ts-node main.ts", "test": "jest --config config/jest.config.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.24.0", "azure-pipelines-task-lib": "^4.7.0", "gpt-tokenizer": "^2.1.2", "lodash": "^4.17.21", "micromatch": "^4.0.8", "openai": "^4.80.0", "parse-git-diff": "^0.0.17", "simple-git": "^3.21.0"}, "devDependencies": {"@dotenvx/dotenvx": "^1.36.0", "@types/jest": "^29.0.0", "@types/micromatch": "^4.0.9", "@types/node": "^22.7.2", "jest": "^29.0.0", "tfx-cli": "^0.18.0", "ts-jest": "^29.0.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}