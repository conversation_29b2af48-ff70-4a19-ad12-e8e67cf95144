import { Review } from '../types/review';
import { PRSummary } from '../services/pr-service';
import { PRAnalysis } from '../services/git-service';
import { PRContext } from '../services/ai-service';

export interface AIProviderConfig {
    apiKey: string;
    model?: string;
    maxTokens?: number;
    temperature?: number;
    additionalPrompts?: string[];
    bugs?: boolean;
    performance?: boolean;
    bestPractices?: boolean;
    modifiedLinesOnly?: boolean;
}

export abstract class BaseAIProvider {
    protected config: AIProviderConfig;
    protected maxTokens: number = 128000;

    constructor(config: AIProviderConfig) {
        this.config = config;
        this.maxTokens = config.maxTokens || 128000;
    }

    public abstract get name(): string;
    
    public abstract performCodeReview(
        diff: string, 
        fileName: string, 
        existingComments: string[]
    ): Promise<Review>;
    
    public abstract generatePRSummary(
        analysis: PRAnalysis, 
        fullDiff: string, 
        prContext: PRContext
    ): Promise<PRSummary>;
    
    public abstract isAvailable(): boolean;

    protected buildCodeReviewPrompt(): string {
        const { bugs, performance, bestPractices, modifiedLinesOnly, additionalPrompts } = this.config;
        
        let prompt = `Your task is to act as a code reviewer of a pull request within Azure DevOps.
        - You are provided with the code changes (diff) in a Unified Diff format.
        - You are provided with a file path (fileName).
        - You are provided with existing comments (existingComments) on the file, you must provide any additional code review comments that are not duplicates.
        - Do not highlight minor issues and nitpicks.
        ${modifiedLinesOnly ? '- Only comment on modified lines.' : ''}
        ${bugs ? '- If there are any bugs, highlight them.' : ''}
        ${performance ? '- If there are major performance problems, highlight them.' : ''}
        ${bestPractices ? '- Provide details on missed use of best-practices.' : '- Do not provide comments on best practices.'}`;

        if (additionalPrompts && additionalPrompts.length > 0) {
            prompt += '\n' + additionalPrompts.map(p => `- ${p}`).join('\n');
        }

        prompt += `

The response should be a single JSON object (without fenced codeblock) and it must use this sample JSON format:
{
    "threads": [
        // Use multiple, separate thread objects for distinct comments at different locations. Line and offset references should be as specific as possible.
        {
            "comments": [
                {
                    "content": "<Comment in markdown format without markdown fenced codeblock>",
                    "commentType": 2
                }
            ],
            "status": 1,
            "threadContext": {
                "filePath": "<string>", //path to file
                //only include leftFile properties for suggestions on unmodified lines
                "leftFileStart": {
                    "line": <integer>, //line where the suggestion starts
                    "offset": <integer>, //character offset where the suggestion starts
                    "snippet": "<code snippet for suggestion>"
                },
                "leftFileEnd": {
                    "line": <integer>, //line where the suggestion ends
                    "offset": <integer>, //character offset where the suggestion ends
                },
                //only use rightFile properties if the line changed in the diff
                "rightFileStart": {
                    "line": <integer>, //line where the suggestion starts
                    "snippet": "<code snippet for suggestion>",
                    "offset": <integer>, //character offset where the suggestion starts
                },
                "rightFileEnd": {
                    "line": <integer>, //line where the suggestion ends
                    "offset": <integer>, //character offset where the suggestion ends
                }
            }
        }
    ]
}`;

        return prompt;
    }

    protected buildPRSummaryPrompt(style: 'concise' | 'detailed' | 'executive' = 'detailed'): string {
        const styleInstructions = {
            concise: 'Keep the summary brief and to the point. Focus on the most important changes only.',
            detailed: 'Provide a comprehensive summary with detailed analysis of changes and their implications.',
            executive: 'Write for senior stakeholders. Focus on business impact, risks, and high-level technical decisions.'
        };

        return `You are an expert code reviewer tasked with creating a pull request summary.

STYLE: ${styleInstructions[style]}

Analyze the provided pull request information and create a summary with the following structure:

1. **Title**: A clear, descriptive title for the changes
2. **Overview**: A paragraph explaining what this PR accomplishes
3. **Key Changes**: A bulleted list of the most important modifications
4. **Impact**: Assessment of the change's impact on the codebase and users
5. **Recommendations**: Optional suggestions for reviewers or next steps

Guidelines:
- Be accurate and objective
- Highlight potential risks or areas needing attention
- Consider the technical complexity and business value
- Use clear, professional language
- Focus on what matters most to reviewers

Respond with a JSON object in this exact format:
{
    "title": "Clear descriptive title",
    "overview": "Comprehensive overview paragraph",
    "keyChanges": ["Change 1", "Change 2", "Change 3"],
    "impact": "Impact assessment paragraph",
    "recommendations": ["Recommendation 1", "Recommendation 2"]
}`;
    }

    protected doesMessageExceedTokenLimit(message: string, tokenLimit: number): boolean {
        // Simple approximation: 4 characters per token
        const estimatedTokens = message.length / 4;
        return estimatedTokens > tokenLimit;
    }

    protected truncateContent(content: string, maxTokens: number): string {
        const maxChars = maxTokens * 4; // Rough approximation
        if (content.length <= maxChars) {
            return content;
        }

        // Try to truncate at a reasonable boundary
        const truncated = content.substring(0, maxChars);
        const lastNewline = truncated.lastIndexOf('\n');
        
        if (lastNewline > maxChars * 0.8) {
            return truncated.substring(0, lastNewline) + '\n\n[Content truncated due to length]';
        }
        
        return truncated + '\n\n[Content truncated due to length]';
    }

    protected formatUserPrompt(data: any): string {
        return JSON.stringify(data, null, 2);
    }
}
