# This env file is used for local development.  It's not meant for production.  
# To use this, copy this file to .env.local and update the values as needed.

#--- Azure DevOps and PR Setup ---
Build_Reason=PullRequest
# OpenAI API Key
Api_Key=<your_api_key>
# OpenAI Model to use for code review
Ai_Model=o3-mini

# Azure DevOps Bearer Token.  Used to authenticate to Azure DevOps API
System_AccessToken=<your_access_token>
# Set to the root of your local git repo to be reviewed
System_DefaultWorkingDirectory=<your_local_git_repo_path>
# Set to the source branch to be reviewed against.  This should match your PR source branch.
System_PullRequest_SourceBranch=<your_source_branch>
# Set to the target branch name to be reviewed against.  Typically, this is master.
System_PullRequest_TargetBranchName=master
# Set to the URL of your Azure DevOps instance
System_TeamFoundationCollectionUri=<your_azure_devops_url>
# Set to the Team Project ID
System_TeamProjectId=<your_team_project_id>
# Set to the name of the repository
Build_Repository_Name=<your_repo_name>
# Set to the Pull Request ID to review
System_PullRequest_PullRequestId=<your_pull_request_id>
# OpenAI struggles with determining the code line number and offset to which the suggestion applies. 
# Setting this to true will use our own algorithm to determine the line number and offset.
Comment_Line_Correction=true
# Set to true to allow requeues.  This will allow PRIA to review requeued PRs.
Allow_Requeue=false


#--- Code Review Options ---
# Set to true to review for bugs
Bugs=true
# Set to true to review for performance problems
Performance=true
# Set to true to review for best practices
Best_Practices=false
# Set to true to only review modified lines
Modified_Lines_Only=true

#--- Development Options ---
# Set to true to allow local dev testing of PRIA. 
# Setting to false will cause PRIA to run same as if it were in a PR build and will also invalidate the rest of the env vars in this file.
Dev_Mode=true
# Experimental.  This is used to automatically set up the current branch to the PR branch.
Auto_Setup_PR_Branch=false
# Set to true to enable verbose logging
Verbose_Logging=false
# Uncomment to enable simple-git debug logging.  Additional options can be found here: https://github.com/steveukx/git-js/blob/main/docs/DEBUG-LOGGING-GUIDE.md#verbose-logging-options
#DEBUG=simple-git