import tl from '../taskWrapper';

export interface TaskConfig {
    // Existing PRIA config
    apiKey: string;
    azureApiEndpoint?: string;
    azureApiVersion?: string;
    azureModelDeployment?: string;
    fileExtensions?: string;
    fileExtensionExcludes?: string;
    filesToInclude?: string;
    filesToExclude?: string;
    additionalPrompts?: string[];
    bugs: boolean;
    performance: boolean;
    bestPractices: boolean;
    modifiedLinesOnly: boolean;
    enableCommentLineCorrection: boolean;
    allowRequeue: boolean;

    // New MVP config
    aiProvider: 'openai' | 'anthropic' | 'azure-openai';
    anthropicApiKey?: string;
    generateSummary: boolean;
    summaryStyle: 'concise' | 'detailed' | 'executive';
    updatePrDescription: boolean;
    summaryPlacement: 'top' | 'bottom' | 'replace';
    reviewScope: 'all' | 'modified-only' | 'new-files';
    maxFilesPerReview: number;
    enableChunking: boolean;
}

export class ConfigService {
    private static _instance: ConfigService;
    private _config: TaskConfig;

    private constructor() {
        this._config = this.loadConfiguration();
        this.validateConfiguration();
    }

    public static getInstance(): ConfigService {
        if (!ConfigService._instance) {
            ConfigService._instance = new ConfigService();
        }
        return ConfigService._instance;
    }

    public get config(): TaskConfig {
        return { ...this._config };
    }

    private loadConfiguration(): TaskConfig {
        return {
            // Existing PRIA inputs
            apiKey: tl.getInput('api_key', true)!,
            azureApiEndpoint: tl.getInput('api_endpoint', false) || undefined,
            azureApiVersion: tl.getInput('api_version', false) || undefined,
            azureModelDeployment: tl.getInput('ai_model', false) || undefined,
            fileExtensions: tl.getInput('file_extensions', false) || undefined,
            fileExtensionExcludes: tl.getInput('file_extension_excludes', false) || undefined,
            filesToInclude: tl.getInput('file_includes', false) || undefined,
            filesToExclude: tl.getInput('file_excludes', false) || undefined,
            additionalPrompts: tl.getInput('additional_prompts', false)?.split(',') || [],
            bugs: tl.getBoolInput('bugs', false),
            performance: tl.getBoolInput('performance', false),
            bestPractices: tl.getBoolInput('best_practices', false),
            modifiedLinesOnly: tl.getBoolInput('modified_lines_only', false),
            enableCommentLineCorrection: tl.getBoolInput('comment_line_correction', false),
            allowRequeue: tl.getBoolInput('allow_requeue', false),

            // New MVP inputs with defaults
            aiProvider: (tl.getInput('ai_provider', false) as any) || 'openai',
            anthropicApiKey: tl.getInput('anthropic_api_key', false) || undefined,
            generateSummary: tl.getBoolInput('generate_summary', false) ?? true,
            summaryStyle: (tl.getInput('summary_style', false) as any) || 'detailed',
            updatePrDescription: tl.getBoolInput('update_pr_description', false) ?? true,
            summaryPlacement: (tl.getInput('summary_placement', false) as any) || 'top',
            reviewScope: (tl.getInput('review_scope', false) as any) || 'modified-only',
            maxFilesPerReview: parseInt(tl.getInput('max_files_per_review', false) || '20'),
            enableChunking: tl.getBoolInput('enable_chunking', false) ?? true,
        };
    }

    private validateConfiguration(): void {
        const errors: string[] = [];

        // Validate API keys based on provider
        if (this._config.aiProvider === 'anthropic' && !this._config.anthropicApiKey) {
            errors.push('anthropic_api_key is required when ai_provider is set to "anthropic"');
        }

        if ((this._config.aiProvider === 'openai' || this._config.aiProvider === 'azure-openai') && !this._config.apiKey) {
            errors.push('api_key is required for OpenAI and Azure OpenAI providers');
        }

        // Validate Azure OpenAI specific config
        if (this._config.aiProvider === 'azure-openai') {
            if (!this._config.azureApiEndpoint) {
                errors.push('api_endpoint is required when using Azure OpenAI');
            }
            if (!this._config.azureApiVersion) {
                errors.push('api_version is required when using Azure OpenAI');
            }
        }

        // Validate enum values
        const validProviders = ['openai', 'anthropic', 'azure-openai'];
        if (!validProviders.includes(this._config.aiProvider)) {
            errors.push(`ai_provider must be one of: ${validProviders.join(', ')}`);
        }

        const validSummaryStyles = ['concise', 'detailed', 'executive'];
        if (!validSummaryStyles.includes(this._config.summaryStyle)) {
            errors.push(`summary_style must be one of: ${validSummaryStyles.join(', ')}`);
        }

        const validPlacements = ['top', 'bottom', 'replace'];
        if (!validPlacements.includes(this._config.summaryPlacement)) {
            errors.push(`summary_placement must be one of: ${validPlacements.join(', ')}`);
        }

        const validScopes = ['all', 'modified-only', 'new-files'];
        if (!validScopes.includes(this._config.reviewScope)) {
            errors.push(`review_scope must be one of: ${validScopes.join(', ')}`);
        }

        // Validate numeric values
        if (this._config.maxFilesPerReview < 1 || this._config.maxFilesPerReview > 100) {
            errors.push('max_files_per_review must be between 1 and 100');
        }

        if (errors.length > 0) {
            const errorMessage = `Configuration validation failed:\n${errors.join('\n')}`;
            tl.setResult(tl.TaskResult.Failed, errorMessage);
            throw new Error(errorMessage);
        }

        console.info('Configuration validated successfully');
    }

    public logConfiguration(): void {
        const configToLog = { ...this._config };
        
        // Mask sensitive fields
        if (configToLog.apiKey) configToLog.apiKey = '***';
        if (configToLog.anthropicApiKey) configToLog.anthropicApiKey = '***';

        console.info('Task Configuration:');
        for (const [key, value] of Object.entries(configToLog)) {
            console.info(`  ${key}: ${value}`);
        }
    }
}
