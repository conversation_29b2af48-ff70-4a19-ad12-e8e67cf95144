import tl from './taskWrapper';
import { ConfigService } from './services/config-service';
import { PRService } from './services/pr-service';
import { GitService } from './services/git-service';
import { createAIService } from './services/ai-service';
import { PRSummaryService } from './services/pr-summary-service';
import { filterFilesForReview } from './fileUtils';

export class Main {
    private static configService: ConfigService;
    private static prService: PRService;
    private static gitService: GitService;
    private static aiService: any;
    private static prSummaryService: PRSummaryService;

    public static async main(): Promise<void> {
        try {
            // Initialize services
            await this.initializeServices();

            // Validate environment and trigger
            if (!this.isValidTrigger()) return;

            // Get and validate configuration
            const config = this.configService.config;
            this.configService.logConfiguration();

            // Initialize repository
            await this.gitService.init();
            await this.gitService.setupCurrentBranch();

            // Get review range and check for requeue
            const { reviewRange, isRequeued } = await this.getReviewRange();
            if (isRequeued && !config.allowRequeue) {
                console.info('No new changes detected since last review and requeue is disabled. Skipping pull request review.');
                return;
            }

            // Get files to review
            const iterationFiles = await this.prService.getIterationFiles(reviewRange);
            console.info(`Found ${iterationFiles.length} changed files in this run:`, iterationFiles);

            const filesToReview = this.filterFiles(iterationFiles, config);
            console.info(`After filtering, ${filesToReview.length} files will be reviewed:`, filesToReview);

            if (filesToReview.length === 0) {
                console.info('No files to review after filtering');
                tl.setResult(tl.TaskResult.Succeeded, 'No files to review.');
                return;
            }

            // Generate PR summary if enabled
            if (config.generateSummary) {
                console.info('Generating PR summary...');
                const summarySuccess = await this.prSummaryService.generateAndUpdatePRSummary(filesToReview);
                if (summarySuccess) {
                    console.info('PR summary generated and applied successfully');
                } else {
                    tl.warning('PR summary generation failed, continuing with code review');
                }
            }

            // Perform code review
            await this.reviewFiles(filesToReview);

            // Save last reviewed iteration
            await this.prService.saveLastReviewedIteration(reviewRange);

            tl.setResult(tl.TaskResult.Succeeded, 'Pull Request reviewed successfully.');
        } catch (error) {
            console.error('Main execution failed:', error);
            tl.setResult(tl.TaskResult.Failed, `Pull Request review failed: ${error}`);
        }
    }

    private static async initializeServices(): Promise<void> {
        try {
            // Initialize configuration service first
            this.configService = ConfigService.getInstance();

            // Initialize other services
            this.prService = new PRService();
            this.gitService = new GitService();

            // Initialize AI service with provider
            this.aiService = await createAIService(this.configService.config);

            // Initialize PR summary service
            this.prSummaryService = new PRSummaryService(
                this.aiService,
                this.prService,
                this.gitService,
                this.configService.config
            );

            console.info('All services initialized successfully');
        } catch (error) {
            console.error('Service initialization failed:', error);
            throw error;
        }
    }

    private static isValidTrigger(): boolean {
        if (tl.getVariable('Build.Reason') !== 'PullRequest') {
            tl.setResult(tl.TaskResult.Skipped, 'This task must only be used when triggered by a Pull Request.');
            return false;
        }

        if (!tl.getVariable('System.AccessToken')) {
            tl.setResult(
                tl.TaskResult.Failed,
                "'Allow Scripts to Access OAuth Token' must be enabled. See https://learn.microsoft.com/en-us/azure/devops/pipelines/build/options?view=azure-devops#allow-scripts-to-access-the-oauth-token for more information"
            );
            return false;
        }

        return true;
    }

    private static async getReviewRange() {
        try {
            const lastReviewedIteration = await this.prService.getLastReviewedIteration();
            const latestIterationId = await this.prService.getLatestIterationId();

            let reviewRange = { start: lastReviewedIteration.end, end: latestIterationId };
            const isRequeued = lastReviewedIteration.end === latestIterationId;

            console.info(`Review range: ${reviewRange.start} to ${reviewRange.end}, isRequeued: ${isRequeued}`);

            if (isRequeued) {
                reviewRange = { ...lastReviewedIteration };
            }

            return { reviewRange, isRequeued };
        } catch (error) {
            console.error('Failed to get review range:', error);
            throw error;
        }
    }

    private static filterFiles(iterationFiles: string[], config: any) {
        return filterFilesForReview({
            fileExtensions: config.fileExtensions,
            fileExtensionExcludes: config.fileExtensionExcludes,
            filesToInclude: config.filesToInclude,
            filesToExclude: config.filesToExclude,
            files: iterationFiles,
        });
    }

    private static async reviewFiles(filesToReview: string[]): Promise<void> {
        try {
            tl.setProgress(0, 'Performing Code Review');

            const maxFiles = Math.min(filesToReview.length, this.configService.config.maxFilesPerReview);
            const filesToProcess = filesToReview.slice(0, maxFiles);

            if (filesToProcess.length < filesToReview.length) {
                console.info(`Limiting review to ${maxFiles} files out of ${filesToReview.length} total to manage processing time`);
            }

            for (let index = 0; index < filesToProcess.length; index++) {
                const fileName = filesToProcess[index];

                try {
                    console.info(`Reviewing file ${index + 1}/${filesToProcess.length}: ${fileName}`);

                    const diff = await this.gitService.getDiff(fileName);
                    const existingComments = await this.prService.getCommentsForFile(fileName);

                    console.info(`Found ${existingComments.length} existing comments for ${fileName}`);

                    // Perform code review
                    const reviewComment = await this.aiService.performCodeReview(diff, fileName, existingComments);

                    // Add review comments to the pull request
                    if (reviewComment && reviewComment.threads && reviewComment.threads.length > 0) {
                        for (const thread of reviewComment.threads) {
                            const success = await this.prService.addThread(thread);
                            if (!success) {
                                tl.warning(`Failed to add thread for ${fileName}`);
                            }
                        }
                        console.info(`Added ${reviewComment.threads.length} review threads for ${fileName}`);
                    } else {
                        console.info(`No review comments generated for ${fileName}`);
                    }

                    // Update progress
                    const progress = ((index + 1) / filesToProcess.length) * 100;
                    tl.setProgress(progress, `Reviewed ${index + 1}/${filesToProcess.length} files`);
                } catch (error) {
                    tl.warning(`Failed to review file ${fileName}: ${error}`);
                    // Continue with other files
                }
            }

            console.info(`Code review completed for ${filesToProcess.length} files`);
        } catch (error) {
            console.error('Code review process failed:', error);
            throw error;
        }
    }
}

// Execute main function
Main.main().catch(error => {
    console.error('Unhandled error in main:', error);
    tl.setResult(tl.TaskResult.Failed, `Unhandled error: ${error}`);
});
