import tl from '../taskWrapper';
import { Agent } from 'https';
import { AzureDevOps } from '../azureDevOps';
import { GitPullRequest } from '../types/azureDevOps/gitPullRequest';
import { PropertiesCollection } from '../types/azureDevOps/propertiesCollection';
import { GitPullRequestIterationChanges } from '../types/azureDevOps/gitPullRequestIterationChanges';
import { IterationRange } from '../types/iterationRange';

export interface PRSummary {
    title: string;
    overview: string;
    keyChanges: string[];
    impact: string;
    recommendations?: string[];
}

export class PRService {
    private _httpsAgent: Agent;
    private _collectionUri: string = tl.getVariable('System.TeamFoundationCollectionUri')!;
    private _teamProjectId: string = tl.getVariable('System.TeamProjectId')!;
    private _repositoryName: string = tl.getVariable('Build.Repository.Name')!;
    private _pullRequestId: string = tl.getVariable('System.PullRequest.PullRequestId')!;
    private _pullRequest?: GitPullRequest;
    private _ado: AzureDevOps;

    private static readonly PRIA_LAST_REVIEWED_KEY = 'Pria.LastReviewedIteration';

    constructor() {
        this._httpsAgent = new Agent({
            rejectUnauthorized: false,
        });
        this._ado = new AzureDevOps();
    }

    public getBaseUri(): string {
        return `${this._collectionUri}${this._teamProjectId}/_apis/git/repositories/${this._repositoryName}`;
    }

    public getPullRequestBaseUri(): string {
        return `${this.getBaseUri()}/pullRequests/${this._pullRequestId}`;
    }

    public async getPullRequest(): Promise<GitPullRequest> {
        if (this._pullRequest) return this._pullRequest;

        tl.debug(`Getting pull request ${this._pullRequestId}`);
        const endpoint = `${this.getPullRequestBaseUri()}/?api-version=7.0`;
        this._pullRequest = await this._ado.get<GitPullRequest>(endpoint);
        return this._pullRequest;
    }

    public async getLatestIterationId(): Promise<number> {
        const endpoint = `${this.getPullRequestBaseUri()}/iterations?api-version=7.0`;
        tl.debug(`Fetching iterations for pull request ${this._pullRequestId}`);

        const iterations = await this._ado.get<{ value: { id: number }[] }>(endpoint);
        if (!iterations.value || iterations.value.length === 0) {
            throw new Error(`No iterations found for pull request ${this._pullRequestId}`);
        }

        const latestIteration = Math.max(...iterations.value.map((iteration) => iteration.id));
        console.info(`Latest iteration ID: ${latestIteration}`);
        return latestIteration;
    }

    public async getIterationFiles({ start, end }: IterationRange): Promise<string[]> {
        tl.debug(`Getting files for iteration ${start}-${end}`);

        const endpoint = `${this.getPullRequestBaseUri()}/iterations/${end}/changes?api-version=7.0&$compareTo=${start}`;
        const result = await this._ado.get<GitPullRequestIterationChanges>(endpoint);

        const files = result.changeEntries
            .map(({ item }) => item.path)
            .filter((file) => !!file); // Filter out null paths

        tl.debug(`Files in iteration ${start}-${end}: ${JSON.stringify(files)}`);
        return files;
    }

    public async getLastReviewedIteration(): Promise<IterationRange> {
        const endpoint = `${this.getPullRequestBaseUri()}/properties?api-version=7.0`;
        const properties = await this._ado.get<PropertiesCollection>(endpoint);
        const value = properties.value[PRService.PRIA_LAST_REVIEWED_KEY]?.$value;
        
        if (!value) {
            console.info('No last reviewed iteration found, returning default range.');
            return { start: 0, end: 0 };
        }
        
        const lastReviewedIteration = JSON.parse(value) as IterationRange;
        console.info(`Last reviewed iteration ${value}`);
        return lastReviewedIteration;
    }

    public async saveLastReviewedIteration({ start, end }: IterationRange): Promise<boolean> {
        tl.debug(`Saving last reviewed iteration ${start}-${end}`);
        const endpoint = `${this.getPullRequestBaseUri()}/properties?api-version=7.0`;
        const body = [
            {
                op: 'replace',
                path: `/${PRService.PRIA_LAST_REVIEWED_KEY}`,
                value: JSON.stringify({ start, end } as IterationRange),
            },
        ];

        const response = await this._ado.patch(endpoint, body);
        if (!response.ok) {
            tl.warning(`Failed to save last reviewed iteration ${start}-${end}.`);
        } else {
            console.info(`Saved last reviewed iteration ${start}-${end}`);
        }

        return response.ok;
    }

    public async updatePRDescription(summary: PRSummary, placement: 'top' | 'bottom' | 'replace'): Promise<boolean> {
        try {
            const pr = await this.getPullRequest();
            const currentDescription = pr.description || '';
            
            const summaryMarkdown = this.formatSummaryAsMarkdown(summary);
            let newDescription: string;

            switch (placement) {
                case 'top':
                    newDescription = `${summaryMarkdown}\n\n---\n\n${currentDescription}`;
                    break;
                case 'bottom':
                    newDescription = `${currentDescription}\n\n---\n\n${summaryMarkdown}`;
                    break;
                case 'replace':
                    newDescription = summaryMarkdown;
                    break;
                default:
                    newDescription = `${summaryMarkdown}\n\n---\n\n${currentDescription}`;
            }

            const endpoint = `${this.getPullRequestBaseUri()}?api-version=7.0`;
            const updateBody = {
                description: newDescription
            };

            const response = await this._ado.patch(endpoint, updateBody);
            
            if (response.ok) {
                console.info('Successfully updated PR description with AI summary');
                return true;
            } else {
                tl.warning(`Failed to update PR description: ${response.statusText}`);
                return false;
            }
        } catch (error) {
            tl.warning(`Error updating PR description: ${error}`);
            return false;
        }
    }

    private formatSummaryAsMarkdown(summary: PRSummary): string {
        let markdown = `# 🤖 AI-Generated PR Summary\n\n`;
        markdown += `## ${summary.title}\n\n`;
        markdown += `${summary.overview}\n\n`;
        
        if (summary.keyChanges.length > 0) {
            markdown += `### Key Changes\n`;
            summary.keyChanges.forEach(change => {
                markdown += `- ${change}\n`;
            });
            markdown += `\n`;
        }
        
        markdown += `### Impact\n${summary.impact}\n\n`;
        
        if (summary.recommendations && summary.recommendations.length > 0) {
            markdown += `### Recommendations\n`;
            summary.recommendations.forEach(rec => {
                markdown += `- ${rec}\n`;
            });
            markdown += `\n`;
        }
        
        markdown += `*Generated by PR Inspection Assistant*`;
        return markdown;
    }

    // Legacy methods for backward compatibility
    public async addThread(thread: any): Promise<boolean> {
        const endpoint = `${this.getPullRequestBaseUri()}/threads?api-version=7.0`;
        const response = await this._ado.post(endpoint, thread);

        if (!response.ok && response.status === 401) {
            tl.setResult(
                tl.TaskResult.Failed,
                "The Build Service must have 'Contribute to pull requests' access to the repository. See https://stackoverflow.com/a/57985733 for more information"
            );
        }

        return response.ok;
    }

    public async getCommentsForFile(fileName: string): Promise<string[]> {
        if (!fileName.startsWith('/')) {
            fileName = `/${fileName}`;
        }
        
        const collectionName = this._collectionUri.replace('https://', '').replace('http://', '').split('/')[1];
        const buildServiceName = `${tl.getVariable('SYSTEM.TEAMPROJECT')} Build Service (${collectionName})`;

        const threads = await this.getThreads();
        const comments: string[] = [];

        for (const thread of threads as any[]) {
            if (thread.threadContext && thread.threadContext.filePath === fileName) {
                const threadComments = await this.getComments(thread);
                for (const comment of threadComments.value as any[]) {
                    comments.push(comment.content);
                }
            }
        }

        return comments;
    }

    private async getThreads(): Promise<any[]> {
        const threadsEndpoint = `${this.getPullRequestBaseUri()}/threads?api-version=5.1`;
        const threads = await this._ado.get(threadsEndpoint);
        return threads.value.filter((thread: any) => thread.threadContext !== null);
    }

    private async getComments(thread: any): Promise<any> {
        const commentsEndpoint = `${this.getPullRequestBaseUri()}/threads/${thread.id}/comments?api-version=5.1`;
        return await this._ado.get(commentsEndpoint);
    }
}
