import { TaskConfig } from './config-service';
import { PRSummary } from './pr-service';
import { PRAnalysis } from './git-service';
import { Review } from '../types/review';

export interface AIProvider {
    name: string;
    performCodeReview(diff: string, fileName: string, existingComments: string[]): Promise<Review>;
    generatePRSummary(analysis: PRAnalysis, fullDiff: string, prContext: PRContext): Promise<PRSummary>;
    isAvailable(): boolean;
}

export interface PRContext {
    title: string;
    description: string;
    author: string;
    sourceBranch: string;
    targetBranch: string;
    commitMessages: string[];
}

export class AIService {
    private provider: AIProvider;

    constructor(provider: AIProvider) {
        this.provider = provider;
    }

    public async performCodeReview(diff: string, fileName: string, existingComments: string[]): Promise<Review> {
        try {
            if (!this.provider.isAvailable()) {
                throw new Error(`AI provider ${this.provider.name} is not available`);
            }

            console.info(`Performing code review for ${fileName} using ${this.provider.name}`);
            const review = await this.provider.performCodeReview(diff, fileName, existingComments);
            
            console.info(`Code review completed for ${fileName}. Found ${review.threads?.length || 0} issues`);
            return review;
        } catch (error) {
            console.error(`Code review failed for ${fileName}: ${error}`);
            // Return empty review to allow processing to continue
            return { threads: [] };
        }
    }

    public async generatePRSummary(analysis: PRAnalysis, fullDiff: string, prContext: PRContext): Promise<PRSummary> {
        try {
            if (!this.provider.isAvailable()) {
                throw new Error(`AI provider ${this.provider.name} is not available`);
            }

            console.info(`Generating PR summary using ${this.provider.name}`);
            const summary = await this.provider.generatePRSummary(analysis, fullDiff, prContext);
            
            console.info(`PR summary generated successfully`);
            return summary;
        } catch (error) {
            console.error(`PR summary generation failed: ${error}`);
            // Return fallback summary
            return this.createFallbackSummary(analysis, prContext);
        }
    }

    private createFallbackSummary(analysis: PRAnalysis, prContext: PRContext): PRSummary {
        const keyChanges = analysis.fileChanges.slice(0, 5).map(change => {
            const action = change.changeType === 'added' ? 'Added' : 
                          change.changeType === 'deleted' ? 'Deleted' : 'Modified';
            return `${action} ${change.fileName} (+${change.linesAdded}/-${change.linesDeleted})`;
        });

        return {
            title: `Pull Request: ${prContext.title}`,
            overview: `This pull request contains ${analysis.totalFiles} file changes with ${analysis.totalLinesAdded} additions and ${analysis.totalLinesDeleted} deletions across ${analysis.languages.join(', ')} files.`,
            keyChanges,
            impact: `Medium impact change affecting ${analysis.totalFiles} files. Review recommended for ${analysis.languages.join(', ')} components.`,
            recommendations: [
                'Review the changes carefully',
                'Test the functionality thoroughly',
                'Ensure all tests pass'
            ]
        };
    }

    public getProviderName(): string {
        return this.provider.name;
    }

    public isProviderAvailable(): boolean {
        return this.provider.isAvailable();
    }
}

// Factory function to create AI service with appropriate provider
export async function createAIService(config: TaskConfig): Promise<AIService> {
    const { ProviderFactory } = await import('../providers/provider-factory');
    const provider = ProviderFactory.createProvider(config);
    return new AIService(provider);
}
